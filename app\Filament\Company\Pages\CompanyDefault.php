<?php

namespace App\Filament\Company\Pages;

use Filament\Forms\Form;
use Filament\Pages\Page;
use Livewire\Attributes\Locked;
use function Filament\authorize;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Auth\Access\AuthorizationException;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Symfony\Component\Intl\Languages;
use Symfony\Component\Intl\Timezones;
use App\Enums\DateFormat;
use App\Enums\WeekStart;
use App\Filament\Company\Clusters\Settings;
use App\Models\CompanyDefault as CompanyDefaultModel;

class CompanyDefault extends Page
{
    use InteractsWithForms;

    protected static ?string $cluster = Settings::class;

    protected static ?string $title = 'Config';

    protected static ?string $navigationLabel = 'Config';

    protected static string $view = 'filament.company.pages.company-default';

    /**
     * @var array<string, mixed> | null
     */
    public ?array $data = [];

    #[Locked]
    public ?CompanyDefaultModel $record = null;

    public function getTitle(): string|Htmlable
    {
        return static::$title;
    }

    public static function getNavigationLabel(): string
    {
        return static::$title;
    }

    public function getMaxContentWidth(): MaxWidth|string|null
    {
        return MaxWidth::ScreenTwoExtraLarge;
    }



    public function mount(): void
    {
        $this->record = CompanyDefaultModel::firstOrNew([
            'company_id' => auth()->user()->current_company_id,
        ]);

        abort_unless(static::canView($this->record), 404);

        $this->fillForm();
    }

    public function fillForm(): void
    {
        $data = $this->record->attributesToArray();
        $this->form->fill($data);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('currency_code')
                    ->label('Currency Code')
                    ->disabled()
                    ->formatStateUsing(function ($state) {
                        if (!$state)
                            return null;

                        try {
                            $currency = currency($state);
                            return $state . ' - ' . $currency->getName();
                        } catch (\Exception $e) {
                            return $state;
                        }
                    })
                    ->helperText(function ($state) {
                        if (!$state)
                            return null;

                        try {
                            $currency = currency($state);
                            $symbol = $currency->getSymbol();
                            $precision = $currency->getPrecision();
                            $decimalMark = $currency->getDecimalMark();
                            $thousandsSeparator = $currency->getThousandsSeparator();

                            return "Symbol: {$symbol} | Precision: {$precision} | Decimal: '{$decimalMark}' | Thousands: '{$thousandsSeparator}'";
                        } catch (\Exception $e) {
                            return 'Currency information not available';
                        }
                    }),

                Forms\Components\TextInput::make('language')
                    ->label('Language')
                    ->disabled()
                    ->formatStateUsing(function ($state) {
                        if (!$state)
                            return null;

                        try {
                            // Utiliser Symfony Intl pour obtenir le nom de la langue
                            $languageName = Languages::getName($state);
                            return "{$state} - {$languageName}";
                        } catch (\Exception $e) {
                            // Fallback si la langue n'est pas trouvée
                            return $state;
                        }
                    }),

                Forms\Components\TextInput::make('timezone')
                    ->label('Timezone')
                    ->disabled()
                    ->formatStateUsing(function ($state) {
                        if (!$state)
                            return null;

                        try {
                            // Utiliser Symfony Intl pour obtenir le nom localisé du fuseau horaire
                            $timezoneName = Timezones::getName($state);
                            $timezone = new \DateTimeZone($state);
                            $now = new \DateTime('now', $timezone);
                            $offset = $now->format('P');
                            $abbreviation = $now->format('T');

                            return "{$state} - {$timezoneName} ({$abbreviation}, UTC{$offset})";
                        } catch (\Exception $e) {
                            return $state;
                        }
                    }),

                Forms\Components\TextInput::make('date_format')
                    ->label('Date Format')
                    ->disabled()
                    ->formatStateUsing(function ($state) {
                        if (!$state)
                            return null;

                        try {
                            // Essayer d'utiliser l'enum DateFormat pour obtenir le label
                            if (enum_exists(DateFormat::class)) {
                                $dateFormats = DateFormat::cases();
                                foreach ($dateFormats as $format) {
                                    if ($format->value === $state) {
                                        $example = now()->format($state);
                                        return "{$state} - {$format->getLabel()} (Example: {$example})";
                                    }
                                }
                            }

                            // Fallback : juste l'exemple
                            $example = now()->format($state);
                            return "{$state} - Example: {$example}";
                        } catch (\Exception $e) {
                            return $state;
                        }
                    }),

                Forms\Components\TextInput::make('week_start')
                    ->label('Week Start')
                    ->disabled()
                    ->formatStateUsing(function ($state) {
                        if (!$state)
                            return null;

                        // Utiliser l'enum WeekStart si disponible
                        try {
                            if (enum_exists(WeekStart::class)) {
                                $weekStart = WeekStart::from((int) $state);
                                return "{$state} - {$weekStart->getLabel()}";
                            }
                        } catch (\Exception $e) {
                            // Fallback si l'enum n'existe pas ou échoue
                        }

                        // Fallback manuel
                        $days = [
                            0 => 'Sunday',
                            1 => 'Monday',
                            2 => 'Tuesday',
                            3 => 'Wednesday',
                            4 => 'Thursday',
                            5 => 'Friday',
                            6 => 'Saturday',
                        ];

                        return isset($days[$state]) ? $state . ' - ' . $days[$state] : $state;
                    }),

            ])
            ->columns(2)
            ->model($this->record)
            ->statePath('data')
            ->disabled();
    }



    public static function canView(Model $record): bool
    {
        try {
            return authorize('update', $record)->allowed();
        } catch (AuthorizationException $exception) {
            return $exception->toResponse()->allowed();
        }
    }
}
