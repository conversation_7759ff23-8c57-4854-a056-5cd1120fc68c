<?php

namespace App\Filament\Company\Resources\SimpleInvoiceResource\Pages;

use App\Models\SimpleInvoice;
use Filament\Resources\Pages\CreateRecord;
use App\Filament\Company\Resources\SimpleInvoiceResource;

class CreateSimpleInvoice extends CreateRecord
{
    protected static string $resource = SimpleInvoiceResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate totals based on line items
        $lineItems = $data['lineItems'] ?? [];

        $subtotal = 0;
        $taxTotal = 0;
        $discountTotal = 0;

        foreach ($lineItems as $index => $item) {
            $quantity = (float) ($item['quantity'] ?? 0);
            $unitPrice = (float) ($item['unit_price'] ?? 0);
            $taxRate = (float) ($item['tax_rate'] ?? 0);
            $discountRate = (float) ($item['discount_rate'] ?? 0);

            // Calcul du sous-total de la ligne
            $lineSubtotal = $quantity * $unitPrice;
            $subtotal += $lineSubtotal;

            // Calcul des remises (seulement si per line item)
            $lineDiscountAmount = 0;
            $discountMethod = \App\Enums\DocumentDiscountMethod::parse($data['discount_method'] ?? 'per_document');
            if ($discountMethod?->isPerLineItem()) {
                $discountComputation = \App\Enums\AdjustmentComputation::parse($item['discount_computation'] ?? 'percentage');

                if ($discountComputation->isPercentage()) {
                    $lineDiscountAmount = $lineSubtotal * ($discountRate / 100);
                } else {
                    $lineDiscountAmount = $discountRate;
                }
                $discountTotal += $lineDiscountAmount;
            }

            // Calcul des taxes sur le montant après remise
            $taxableAmount = $lineSubtotal - $lineDiscountAmount;
            $lineTaxAmount = $taxableAmount * ($taxRate / 100);
            $taxTotal += $lineTaxAmount;

            // Convertir les valeurs en centimes pour la base de données
            $data['lineItems'][$index]['quantity'] = (int) ($quantity * 100);
            $data['lineItems'][$index]['unit_price'] = (int) ($unitPrice * 100);
            $data['lineItems'][$index]['line_total'] = (int) (($lineSubtotal + $lineTaxAmount - $lineDiscountAmount) * 100);
            $data['lineItems'][$index]['total_excl_tax'] = $lineSubtotal - $lineDiscountAmount;
            $data['lineItems'][$index]['total_incl_tax'] = $lineSubtotal + $lineTaxAmount - $lineDiscountAmount;
            $data['lineItems'][$index]['line_number'] = $index + 1;
        }

        $total = $subtotal + $taxTotal - $discountTotal;

        // Convertir les totaux en centimes pour la base de données
        $data['subtotal'] = (int) ($subtotal * 100);
        $data['tax_total'] = (int) ($taxTotal * 100);
        $data['discount_total'] = (int) ($discountTotal * 100);
        $data['total'] = (int) ($total * 100);
        $data['amount_due'] = (int) ($total * 100);
        $data['amount_paid'] = 0;

        return $data;
    }
}
