# Copilot Instructions for ERPS Laravel Project

## Project Overview

-   This is a Laravel-based ERP system. The codebase follows Laravel conventions for routing, controllers, models, and Blade views, but includes custom business logic and UI components for company document management.
-   Main business logic is in `app/` (DTOs, Services, Models, Actions, etc.).
-   UI templates and components are in `resources/views/filament/company/components/document-templates/`.
-   The project uses Filament, Livewire, and Tailwind CSS for modern UI and admin features.

## Key Architectural Patterns

-   **DTOs**: Data Transfer Objects are in `app/DTO/` and are used to pass structured data between layers.
-   **Blade Components**: Custom Blade components (e.g., `<x-company.document-template.*>`) are used for document rendering. See `resources/views/filament/company/components/document-templates/` for examples.
-   **Conditional Rendering**: Blade templates use many `@if` and dynamic property checks (e.g., `$document->showHeader`, `$document->lineItemColumns->isDelivery()`).
-   **Accent Color & Theming**: Document templates use `$document->accentColor` for dynamic theming.
-   **Company/Client Data**: Accessed via `$document->company`, `$document->client`, and related DTOs.

## Developer Workflows

-   **Install dependencies**: `composer install` (PHP), `npm install` (JS/CSS)
-   **Build assets**: `npm run build` (for production), `npm run dev` (for development with Vite)
-   **Run tests**: `php artisan test` or `vendor/bin/phpunit`
-   **Serve app**: `php artisan serve` (or use XAMPP/Apache for local dev)
-   **Migrate DB**: `php artisan migrate`

## Project-Specific Conventions

-   **Document Templates**: All document rendering logic is in Blade components under `resources/views/filament/company/components/document-templates/`.
-   **DTO Usage**: Always use DTOs for passing data to views/components, not raw arrays.
-   **Component Naming**: Blade components use dot notation and are prefixed with `x-company.document-template.*`.
-   **Dynamic Styling**: Use inline `style` attributes for dynamic colors, especially for document accenting.
-   **Localization**: Use `$document->label->*` for field labels to support multi-language documents.

## Integration Points

-   **Filament**: Used for admin panels and UI widgets.
-   **Livewire**: For reactive UI components (check `app/Filament/` and `resources/views/filament/`).
-   **Tailwind CSS**: All UI is styled with Tailwind; see `tailwind.config.js` for customizations.
-   **External Packages**: Check `composer.json` and `package.json` for additional dependencies (e.g., Squire, Spatie, etc.).

## Examples

-   To add a new document field, update the relevant DTO in `app/DTO/`, then update the Blade template in `resources/views/filament/company/components/document-templates/`.
-   To customize document appearance, edit the Blade component and use `$document->accentColor` for theming.

## References

-   Main entry: `routes/web.php`, `app/Http/Controllers/`, `resources/views/`
-   Document templates: `resources/views/filament/company/components/document-templates/`
-   DTOs: `app/DTO/`
-   Services: `app/Services/`

---

For any unclear conventions or missing documentation, ask the maintainers or check the latest code in the referenced directories.
