<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use App\Enums\InvoiceStatus;
use App\Enums\PaymentTerms;
use App\Enums\DocumentDiscountMethod;
use App\Enums\AdjustmentComputation;
use App\Traits\Blamable;
use App\Traits\CompanyOwned;

class SimpleInvoice extends Model
{
    use HasFactory, Blamable, CompanyOwned;

    protected $fillable = [
        'company_id',
        'client_id',
        'invoice_number',
        'order_number',
        'date',
        'due_date',
        'payment_terms',
        'status',
        'currency_code',
        'discount_method',
        'discount_computation',
        'discount_rate',
        'subtotal',
        'tax_total',
        'discount_total',
        'total',
        'amount_paid',
        'approved_at',
        'last_sent_at',
        'paid_at',
        'last_viewed_at',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'date' => 'date',
        'due_date' => 'date',
        'payment_terms' => PaymentTerms::class,
        'status' => InvoiceStatus::class,
        'discount_method' => DocumentDiscountMethod::class,
        'discount_computation' => AdjustmentComputation::class,
        'discount_rate' => 'decimal:2',
        'approved_at' => 'datetime',
        'last_sent_at' => 'datetime',
        'paid_at' => 'datetime',
        'last_viewed_at' => 'datetime',
        'subtotal' => 'integer',
        'tax_total' => 'integer',
        'discount_total' => 'integer',
        'total' => 'integer',
        'amount_paid' => 'integer',
        'amount_due' => 'integer',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    public function lineItems(): HasMany
    {
        return $this->hasMany(SimpleInvoiceLineItem::class, 'simple_invoice_id');
    }

    public static function getNextDocumentNumber(): string
    {
        $lastInvoice = static::orderBy('id', 'desc')->first();
        $lastNumber = $lastInvoice ? (int) substr($lastInvoice->invoice_number, 3) : 0;

        return 'INV-' . str_pad($lastNumber + 1, 6, '0', STR_PAD_LEFT);
    }

    public function hasPayments(): bool
    {
        return $this->amount_paid > 0;
    }

    public function isPaid(): bool
    {
        return $this->status === InvoiceStatus::Paid;
    }

    public function isOverdue(): bool
    {
        return $this->due_date->isPast() && !$this->isPaid();
    }
}
